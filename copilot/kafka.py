from aiokafka.errors import (
    CommitFailedError, KafkaTimeoutError, RequestTimedOutError,
    KafkaUnavailableError, NotLeaderForPartitionError, LeaderNotAvailableError,
    NodeNotReadyError, RebalanceInProgressError, NotCoordinatorForGroupError,
    KafkaConnectionError, NoBrokersAvailable, MetadataEmptyBrokerList,
    TopicAuthorizationFailedError, InvalidTopicError, OffsetOutOfRangeError,
    RecordTooLargeError, UnsupportedVersionError, IllegalStateError,
    InvalidGroupIdError, ProducerClosed, ProducerFenced, UnknownTopicOrPartitionError,
    CorruptRecordException, GroupCoordinatorNotAvailableError,
    NotEnoughReplicasError, NotEnoughReplicasAfterAppendError,
)
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer, TopicPartition
from aiokafka.consumer.subscription_state import ConsumerRebalanceListener
import functools
import logging
import asyncio
from typing import Any, Callable, Optional, Type, Union

logger = logging.getLogger(__name__)

RETRYABLE_ERRORS = (
    KafkaTimeoutError,
    RequestTimedOutError,
    NotLeaderForPartitionError,
    LeaderNotAvailableError,
    UnknownTopicOrPartitionError,
    GroupCoordinatorNotAvailableError,
    NodeNotReadyError,
    RebalanceInProgressError,
    CommitFailedError,  # 添加可重试的提交错误
)

CONNECTION_REBUILD_ERRORS = (
    KafkaConnectionError,
    NoBrokersAvailable,
    MetadataEmptyBrokerList,
    ProducerClosed,
)

NON_RETRYABLE_ERRORS = (
    TopicAuthorizationFailedError,
    InvalidTopicError,
    RecordTooLargeError,
    CorruptRecordException,
    UnsupportedVersionError,
    IllegalStateError,
    InvalidGroupIdError,
    NotEnoughReplicasError,  # 添加副本不足错误
    NotEnoughReplicasAfterAppendError,  # 添加写入后副本不足错误
)

def handle_kafka_error(
    retries: int = 3,
    retry_interval: float = 1.0,
    max_retry_interval: float = 10.0,
    exponential_backoff: bool = True,
    skip_errors: tuple = (),
    logger: Optional[logging.Logger] = None,
) -> Callable:
    """Kafka 错误处理装饰器

    Args:
        retries (int): 最大重试次数
        retry_interval (float): 初始重试间隔（秒）
        max_retry_interval (float): 最大重试间隔（秒）
        exponential_backoff (bool): 是否使用指数退避策略
        skip_errors (tuple): 跳过处理的错误类型
        logger (Optional[logging.Logger]): 自定义日志记录器

    Returns:
        Callable: 装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            _logger = logger or logging.getLogger(func.__module__)
            attempt = 0
            current_interval = retry_interval

            while True:
                try:
                    return await func(*args, **kwargs)

                except NON_RETRYABLE_ERRORS as e:
                    if isinstance(e, skip_errors):
                        raise
                    _logger.error(f"遇到不可重试的 Kafka 错误: {str(e)}")
                    raise

                except CONNECTION_REBUILD_ERRORS as e:
                    if isinstance(e, skip_errors):
                        raise
                    if attempt >= retries:
                        _logger.error(f"连接重建错误，已达到最大重试次数 {retries}: {str(e)}")
                        raise
                    _logger.warning(f"连接重建错误，正在重试 ({attempt + 1}/{retries}): {str(e)}")
                    # 对于连接错误，可能需要重新初始化客户端
                    await handle_connection_error(args[0])  # args[0] 假设是 self 实例

                except RETRYABLE_ERRORS as e:
                    if isinstance(e, skip_errors):
                        raise
                    if attempt >= retries:
                        _logger.error(f"可重试错误，已达到最大重试次数 {retries}: {str(e)}")
                        raise
                    _logger.warning(f"可重试错误，正在重试 ({attempt + 1}/{retries}): {str(e)}")

                attempt += 1
                if exponential_backoff:
                    current_interval = min(retry_interval * (2 ** (attempt - 1)), max_retry_interval)
                await asyncio.sleep(current_interval)

        return wrapper
    return decorator

async def handle_connection_error(client: Union[AIOKafkaProducer, AIOKafkaConsumer]) -> None:
    """处理连接错误，重新初始化客户端

    Args:
        client: Kafka 生产者或消费者实例
    """
    try:
        await client.stop()
    except Exception as e:
        logger.warning(f"停止客户端时出错: {str(e)}")
    
    try:
        await client.start()
    except Exception as e:
        logger.error(f"重新启动客户端失败: {str(e)}")
        raise

# 使用示例:
# @handle_kafka_error(retries=3, retry_interval=1.0, exponential_backoff=True)
# async def send_message(self, topic: str, message: bytes) -> None:
#     await self.producer.send(topic, message)