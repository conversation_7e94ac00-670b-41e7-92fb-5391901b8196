import json
import time
import signal
import asyncio
import functools
from utils.logger import logger
from config.settings import config
from multiprocessing import Process
from services.AI_essay_correction import run
from aiokafka.errors import (
    CommitFailedError, KafkaError, KafkaConnectionError, NodeNotReadyError, RequestTimedOutError,
    NotEnoughReplicasError, NotEnoughReplicasAfterAppendError, NotLeaderForPartitionError,
    GroupCoordinatorNotAvailableError, GroupLoadInProgressError, CoordinatorNotAvailableError,
    TopicAuthorizationFailedError, GroupAuthorizationFailedError, ClusterAuthorizationFailedError,
    InvalidTopicError, InvalidPartitionsError, InvalidReplicationFactorError, RecordTooLargeError,
    InvalidMessageError, MessageSizeTooLargeError, IllegalStateError, UnsupportedVersionError,
    CorruptRecordException
)
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer, TopicPartition
from aiokafka.consumer.subscription_state import ConsumerRebalanceListener


# 可重试的错误
RETRYABLE_ERRORS = [
    KafkaConnectionError,
    NodeNotReadyError,
    RequestTimedOutError,
    NotEnoughReplicasError,
    NotEnoughReplicasAfterAppendError,
    NotLeaderForPartitionError,
    GroupCoordinatorNotAvailableError,
    GroupLoadInProgressError,
    CoordinatorNotAvailableError,
    CommitFailedError
]

# 需要重建连接的错误
CONNECTION_REBUILD_ERRORS = [
    KafkaConnectionError,
    NodeNotReadyError,
    GroupCoordinatorNotAvailableError
]

# 不可重试的错误
NON_RETRYABLE_ERRORS = [
    TopicAuthorizationFailedError,
    GroupAuthorizationFailedError,
    ClusterAuthorizationFailedError,
    InvalidTopicError,
    InvalidPartitionsError,
    InvalidReplicationFactorError,
    RecordTooLargeError,
    InvalidMessageError,
    MessageSizeTooLargeError,
    IllegalStateError,
    UnsupportedVersionError,
    CorruptRecordException
]


def is_retryable_error(error):
    """
    判断异常是否可重试
    
    参数:
        error: 异常对象
    
    返回:
        bool: 是否可重试
    """
    return any(isinstance(error, err_type) for err_type in RETRYABLE_ERRORS)


def is_connection_rebuild_error(error):
    """
    判断异常是否需要重建连接
    
    参数:
        error: 异常对象
    
    返回:
        bool: 是否需要重建连接
    """
    return any(isinstance(error, err_type) for err_type in CONNECTION_REBUILD_ERRORS)


def kafka_retry(
    max_retries=3,
    retry_interval=1.0,
    max_interval=60.0,
    backoff_factor=2.0,
    retryable_errors=RETRYABLE_ERRORS,
    rebuild_connection=False,
    rebuild_connection_errors=CONNECTION_REBUILD_ERRORS
):
    """
    Kafka 操作特定的重试装饰器
    
    参数:
        max_retries: 最大重试次数
        retry_interval: 初始重试间隔（秒）
        max_interval: 最大重试间隔（秒）
        backoff_factor: 退避因子（用于指数退避）
        retryable_errors: 可重试的异常列表
        rebuild_connection: 是否在特定错误后重建连接
        rebuild_connection_errors: 触发连接重建的异常列表
    
    返回:
        装饰后的异步函数
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapped(cls, *args, **kwargs):
            retries = 0
            last_exception = None
            
            while retries <= max_retries:
                try:
                    return await func(cls, *args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    # 检查是否是可重试的异常
                    if retryable_errors and not any(isinstance(e, err) for err in retryable_errors):
                        logger.error(f"Non-retryable error in {func.__name__}: {e}")
                        raise
                    
                    # 检查是否需要重建连接
                    if rebuild_connection and rebuild_connection_errors and any(isinstance(e, err) for err in rebuild_connection_errors):
                        if hasattr(cls, 'rebuild_connection'):
                            logger.info(f"Rebuilding connection due to error: {e}")
                            await cls.rebuild_connection(*args, **kwargs)
                    
                    # 检查是否达到最大重试次数
                    if retries >= max_retries:
                        logger.error(f"Max retries ({max_retries}) reached for {func.__name__}: {e}")
                        raise
                    
                    # 计算等待时间并重试
                    retries += 1
                    wait_time = min(retry_interval * (backoff_factor ** (retries - 1)), max_interval)
                    
                    logger.warning(f"Retrying {func.__name__} after error: {e}. Retry {retries}/{max_retries} in {wait_time:.2f}s")
                    
                    await asyncio.sleep(wait_time)
            
            # 不应该到达这里，但为了安全起见
            if last_exception:
                raise last_exception
            return None
        
        return wrapped
    
    return decorator


class AIOConsumerRebalanceListener(ConsumerRebalanceListener):
    """处理rebalance事件的监听器"""
    def __init__(self, on_revoke_cb, on_assign_cb):
        self.on_revoke_cb = on_revoke_cb
        self.on_assign_cb = on_assign_cb
    
    async def on_partitions_revoked(self, revoked):
        if self.on_revoke_cb:
            await self.on_revoke_cb(revoked)
    
    async def on_partitions_assigned(self, assigned):
        if self.on_assign_cb:
            await self.on_assign_cb(assigned)


class KafkaConsumerService:
    _consumer = None
    _partitions_assigned_delay = 5  # 多台服务器消费者加入时间不一致时，可能短时间内多次rebalance并 partitions_assigned，所以需要延迟消费时间(秒)
    running = False
    topics = None
    _failed_commits = {}  # 保存失败的提交，以便后续重试
    
    @classmethod
    async def initialize(cls, group_id, topics, batch_size=1, batch_timeout=1.0):
        """初始化消费者"""
        if cls._consumer is not None:
            return
            
        cls.group_id = group_id
        cls.topics = topics
        cls.batch_size = batch_size
        cls.batch_timeout = batch_timeout
        cls.running = True
        
        bootstrap_servers = config.get("KAFKA").get('bootstrap_servers')
        
        cls._consumer = AIOKafkaConsumer(
            *topics,
            bootstrap_servers=bootstrap_servers,
            group_id=group_id,
            auto_offset_reset=config.get("KAFKA").get("offset_reset"),
            enable_auto_commit=False,
            max_poll_records=batch_size,
            session_timeout_ms=30000,
            max_poll_interval_ms=600000,
            heartbeat_interval_ms=5000,
        )
        listener = AIOConsumerRebalanceListener(
            on_revoke_cb=cls._on_partitions_revoked,
            on_assign_cb=cls._on_partitions_assigned
        )
        # 注册rebalance回调
        cls._consumer.subscribe(
            topics,
            listener=listener
        )
        
        await cls._consumer.start()
        logger.info(f"Kafka consumer initialized for topics: {topics} with group_id: {group_id}")

    @classmethod
    async def rebuild_connection(cls):
        """重建消费者连接"""
        logger.warning(f"Topics: {cls.topics}, Rebuilding consumer connection...")
        
        try:
            # 1. 保存当前状态（如果有必要）
            
            # 2. 关闭现有连接
            if cls._consumer:
                try:
                    await cls._consumer.stop()
                except Exception as e:
                    logger.error(f"Error stopping consumer during rebuild: {e}")
                finally:
                    cls._consumer = None
            
            # 3. 重新初始化连接
            await cls.initialize(
                group_id=cls.group_id,
                topics=cls.topics,
                batch_size=cls.batch_size,
                batch_timeout=cls.batch_timeout
            )
            
            logger.info(f"Topics: {cls.topics}, Consumer connection rebuilt successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to rebuild consumer connection: {e}", exc_info=True)
            return False

    @classmethod
    async def _on_partitions_revoked(cls, revoked):
        """分区被撤销时的回调"""
        logger.warning(f"Topics: {cls.topics}, Partitions revoked: {revoked}")
        
        # 尝试提交失败的偏移量
        if cls._failed_commits:
            await cls._retry_failed_commits()

    @classmethod
    async def _on_partitions_assigned(cls, assigned):
        """分区被分配时的回调"""
        logger.warning(f"Topics: {cls.topics}, Partitions assigned: {assigned}")
        
        # 多台服务器消费者加入时间不一致时，可能短时间内多次rebalance并 partitions_assigned，所以需要延迟消费
        logger.info(f"Topics: {cls.topics}, Waiting {cls._partitions_assigned_delay:.2f}s after partitions_assigned...")
        await asyncio.sleep(cls._partitions_assigned_delay)

    @classmethod
    @kafka_retry(
        max_retries=3,
        retry_interval=1.0,
        backoff_factor=2.0,
        rebuild_connection=True
    )
    async def consume_messages(cls):
        """消费消息并批量处理"""
        if not cls._consumer:
            raise RuntimeError(f"Topics: {cls.topics}, Consumer not initialized. Call initialize() first.")
        
        try:
            # 批量拉取消息
            msgs = await cls._consumer.getmany(timeout_ms=int(cls.batch_timeout * 1000), max_records=cls.batch_size)
            return msgs
        except (KafkaConnectionError, NodeNotReadyError) as e:
            # 连接相关错误，尝试重建连接
            logger.error(f"Topics: {cls.topics}, Connection error: {e}")
            # 这里不需要手动重建连接，重试装饰器会处理
            raise
        except KafkaError as e:
            # 其他 Kafka 错误
            logger.error(f"Topics: {cls.topics}, Kafka error: {e}")
            await asyncio.sleep(1)
            raise
        except Exception as e:
            # 未预期的错误
            logger.error(f"Topics: {cls.topics}, Unexpected error in consumer: {e}", exc_info=True)
            await asyncio.sleep(1)
            raise
    
    @classmethod
    def _get_max_offsets(cls, messages):
        """获取每个分区的最大offset"""
        partitions = {}
        for msg in messages:
            tp = TopicPartition(msg.topic, msg.partition)
            if tp not in partitions or msg.offset > partitions[tp]:
                partitions[tp] = msg.offset
        return partitions
    
    @classmethod
    @kafka_retry(
        max_retries=3,
        retry_interval=1.0,
        backoff_factor=2.0
    )
    async def commit_offsets(cls, messages):
        """提交偏移量"""
        if not messages:
            return
            
        # 取出每个分区的最大偏移量
        partitions = cls._get_max_offsets(messages)
        
        # 为每个分区提交最大偏移量
        for tp, offset in partitions.items():
            try:
                await cls._consumer.commit({tp: offset + 1})  # 提交下一个偏移量
                # 获取提交后的实际偏移量
                committed_offset = await cls._consumer.committed(tp)
                logger.info(f"Committed offset {committed_offset} for Topic: {tp.topic}, Partition: {tp.partition}")
            except CommitFailedError as cfe:
                logger.error(f"Commit failed for {tp} at offset {offset}: {cfe}")
                # 保存失败的提交，以便后续重试
                cls._failed_commits[tp] = offset + 1
                raise
            except Exception as e:
                logger.error(f"Error committing offset for {tp}: {e}")
                cls._failed_commits[tp] = offset + 1
                raise
        
        # 尝试重试之前失败的提交
        if cls._failed_commits:
            await cls._retry_failed_commits()
    
    @classmethod
    async def _retry_failed_commits(cls):
        """重试失败的提交"""
        if not cls._failed_commits:
            return False
        
        success = False
        failed_commits = cls._failed_commits.copy()
        cls._failed_commits.clear()
        
        for tp, offset in failed_commits.items():
            try:
                await cls._consumer.commit({tp: offset})
                # 获取提交后的实际偏移量
                committed_offset = await cls._consumer.committed(tp)
                logger.info(f"Retry committed offset {committed_offset} for Topic: {tp.topic}, Partition: {tp.partition}")
                success = True
            except Exception as e:
                logger.error(f"Retry commit failed for {tp} at offset {offset}: {e}")
                # 重新添加到失败列表
                cls._failed_commits[tp] = offset
        
        return success
    
    @classmethod
    async def check_connection(cls):
        """检查连接状态"""
        if not cls._consumer:
            return False
        
        try:
            # 尝试获取集群元数据作为连接检查
            cluster = cls._consumer.client.cluster
            if not cluster:
                return False
            
            # 检查是否有可用的 broker
            if not cluster.brokers():
                return False
            
            return True
        except Exception as e:
            logger.error(f"Error checking consumer connection: {e}")
            return False
    
    @classmethod
    async def stop(cls):
        """停止消费者"""
        try:
            if cls._consumer:
                # 尝试提交失败的偏移量
                if cls._failed_commits:
                    await cls._retry_failed_commits()
                
                await cls._consumer.stop()
                cls._consumer = None
        except Exception as e:
            logger.error(f"Topics: {cls.topics}, Error stopping consumer: {e}")
        cls.running = False


class KafkaProducerService:
    _producer = None
    _lock = asyncio.Lock()
    
    @classmethod
    async def initialize(cls):
        """创建Kafka生产者"""
        async with cls._lock:
            if cls._producer is None:
                bootstrap_servers = config.get("KAFKA").get('bootstrap_servers')
                cls._producer = AIOKafkaProducer(
                    bootstrap_servers=bootstrap_servers,
                    acks='all',
                    max_batch_size=16384,
                    linger_ms=50,
                )
                await cls._producer.start()
    
    @classmethod
    async def rebuild_connection(cls):
        """重建生产者连接"""
        logger.warning("Rebuilding producer connection...")
        
        try:
            # 1. 关闭现有连接
            if cls._producer:
                try:
                    await cls._producer.stop()
                except Exception as e:
                    logger.error(f"Error stopping producer during rebuild: {e}")
                finally:
                    cls._producer = None
            
            # 2. 重新初始化连接
            await cls.initialize()
            
            logger.info("Producer connection rebuilt successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to rebuild producer connection: {e}", exc_info=True)
            return False
    
    @classmethod
    @kafka_retry(
        max_retries=3,
        retry_interval=1.0,
        backoff_factor=2.0,
        rebuild_connection=True
    )
    async def send_message(cls, topic, message, key=None):
        """发送消息到指定topic"""
        try:
            if cls._producer is None:
                await cls.initialize()
            
            if isinstance(message, (dict, list)):
                message = json.dumps(message, ensure_ascii=False).encode('utf-8')
            else:
                message = str(message).encode('utf-8')
                
            key = key.encode('utf-8') if key else None
            
            await cls._producer.send_and_wait(topic, value=message, key=key)
            return True
        except (KafkaConnectionError, NodeNotReadyError) as e:
            # 连接相关错误，尝试重建连接
            logger.error(f"Connection error when sending to topic {topic}: {e}")
            # 这里不需要手动重建连接，重试装饰器会处理
            raise
        except Exception as e:
            logger.error(f"Failed to send message to topic {topic}: {e}", exc_info=True)
            await cls.close()
            raise
    
    @classmethod
    async def check_connection(cls):
        """检查连接状态"""
        if not cls._producer:
            return False
        
        try:
            # 尝试获取集群元数据作为连接检查
            cluster = cls._producer.client.cluster
            if not cluster:
                return False
            
            # 检查是否有可用的 broker
            if not cluster.brokers():
                return False
            
            return True
        except Exception as e:
            logger.error(f"Error checking producer connection: {e}")
            return False
    
    @classmethod
    async def close(cls):
        """关闭producer连接"""
        try:
            if cls._producer:
                await cls._producer.stop()
                logger.info(f"Producer closed")
        except Exception as e:
            logger.error(f"Failed to close producer: {e}")
        cls._producer = None


class KafkaService:
    @classmethod
    def _signal_handler(cls, signum, frame):
        """处理系统信号"""
        logger.info(f"Received signal {signum}, shutting down...")
        cls.shutdown()

    @classmethod
    def shutdown(cls):
        """优雅关闭所有资源"""
        logger.info("Shutting down Kafka services...")
        KafkaConsumerService.running = False
    
    @classmethod
    async def _process_message(cls, msg, message_processor, produce_topic=None):
        """处理单条消息"""
        start_time = time.time()
        try:
            data = json.loads(msg.value.decode('utf-8'))
            # 记录消息信息
            logger.info(
                f"Processing message - Topic: {msg.topic}, "
                f"Partition: {msg.partition}, "
                f"Offset: {msg.offset}, "
                f"answerId: {data.get('answerId')}"
            )
            # 处理消息
            result = await message_processor(data)
            
            cost_time = time.time() - start_time
            logger.info(f"Process message success - Topic: {msg.topic}, "
                        f"Partition: {msg.partition}, "
                        f"Offset: {msg.offset}, "
                        f"answerId: {data.get('answerId')}, "
                        f"cost time: {cost_time}")
            return result
        except Exception as e:
            cost_time = time.time() - start_time
            logger.error(
                f"Error processing message - Topic: {msg.topic}, "
                f"Partition: {msg.partition}, "
                f"Offset: {msg.offset}, "
                f"message: {msg.value.decode('utf-8')}, "
                f"cost time: {cost_time}, "
                f"Error: {str(e)}",
                exc_info=True
            )
            return None

    @classmethod
    async def process_message_batch(cls, messages, message_processor, produce_topic=None):
        """批量处理消息"""
        if not messages:
            return []
            
        # 使用asyncio.gather并发处理消息
        tasks = [
            cls._process_message(msg, message_processor, produce_topic)
            for msg in messages
        ]
        return await asyncio.gather(*tasks, return_exceptions=True)
    
    @classmethod
    async def produce_results(cls, results, all_messages, produce_topic):
        """批量发送处理结果到目标topic"""
        try:
            for idx, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Task failed with error: {result}, Message: {all_messages[idx]}")
                    continue
                elif result is None:
                    logger.warning(f"No result from processor, Message: {all_messages[idx]}")
                    continue
                
                try:
                    send_result = await KafkaProducerService.send_message(
                        topic=produce_topic,
                        message=result,
                    )
                    if send_result:
                        logger.info(f"Produced message to {produce_topic}, result: {result}")
                    else:
                        logger.warning(f"Failed to produce message to {produce_topic}, result: {result}")
                except Exception as e:
                    logger.error(f"Failed to produce message to {produce_topic}: {e}", exc_info=True)
        except Exception as e:
            logger.error(f"Failed to produce results: {e}", exc_info=True)
    
    @classmethod
    async def _log_consumed_messages(cls, msgs):
        """记录消费的消息信息"""
        if not msgs:
            return [], ""
            
        all_messages = []
        log_message = f"Consuming messages -"
        
        # 处理每个分区的消息
        for tp, messages in msgs.items():
            if not messages:
                continue
            
            # 记录分区的消息消费信息
            first_msg = messages[0]
            last_msg = messages[-1]
            log_message += f"\tTopic: {tp.topic}, Partition: {tp.partition}, Offsets: {first_msg.offset} to {last_msg.offset}, Count: {len(messages)};"                
            all_messages.extend(messages)
        
        return all_messages, log_message
    
    @classmethod
    async def _handle_consumer_iteration(cls, message_processor, produce_topic):
        """处理一次消费者迭代"""
        try:
            # 批量拉取消息
            msgs = await KafkaConsumerService.consume_messages()
            if not msgs:
                return
            
            # 记录消费的消息信息
            all_messages, log_message = await cls._log_consumed_messages(msgs)
            if not all_messages:
                return
                
            logger.info(log_message)
                
            # 异步批量处理消息
            results = await cls.process_message_batch(
                messages=all_messages,
                message_processor=message_processor,
                produce_topic=produce_topic
            )

            # 如果需要发送到其他topic
            if produce_topic:
                await cls.produce_results(results, all_messages, produce_topic)
                
            # 提交偏移量
            await KafkaConsumerService.commit_offsets(all_messages)
        except Exception as e:
            logger.error(f"Error in consumer loop iteration: {e}", exc_info=True)
            # 避免过快重试导致资源消耗
            await asyncio.sleep(1)
    
    @classmethod
    async def start_consumer_loop(cls, group_id, topics, message_processor, produce_topic=None, batch_size=1):
        """
        启动消费者循环
        
        Args:
            group_id: 消费者组ID
            topics: 消费的主题
            message_processor: 消息处理函数
            produce_topic: 处理后发送的目标主题(可选)
            batch_size: 批处理大小
        """
        try:
            signal.signal(signal.SIGINT, cls._signal_handler)
            signal.signal(signal.SIGTERM, cls._signal_handler)
            
            # 初始化消费者
            await KafkaConsumerService.initialize(
                group_id=group_id,
                topics=topics,
                batch_size=batch_size
            )
            
            # 开始消费消息
            while KafkaConsumerService.running:
                await cls._handle_consumer_iteration(message_processor, produce_topic)
                    
        except Exception as e:
            logger.error(f"Failed in consumer loop: {e}", exc_info=True)
        finally:
            await KafkaConsumerService.stop()

    @classmethod
    def start_consuming(cls):
        """启动消费进程"""
        group_id = config["KAFKA"]["group_id"]
        topic = [config["KAFKA"]["insp_data_topic"]]
        produce_topic = config["KAFKA"]["insp_data_result_topic"]
        batch_size = config["KAFKA"]["batch_size"]
        try:
            asyncio.run(cls.start_consumer_loop(group_id, topic, run, produce_topic=produce_topic, batch_size=batch_size))
        except Exception as e:
            logger.error(f"Failed in start consuming: {e}", exc_info=True)
    
    @classmethod
    def start_processes(cls, max_threshold=1):
        """启动多个消费者进程"""
        processes = []
        
        for _ in range(max_threshold):
            p = Process(target=cls.start_consuming)
            p.start()
            processes.append(p)
        
        for p in processes:
            p.join()

