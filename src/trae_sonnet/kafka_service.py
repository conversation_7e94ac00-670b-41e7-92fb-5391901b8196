import json
import time
import signal
import asyncio
from utils.logger import logger
from utils.general_tools import async_retry
from config.settings import config
from multiprocessing import Process
from services.AI_essay_correction import run
from aiokafka.errors import CommitFailedError, KafkaError, KafkaConnectionError, KafkaTimeoutError, LeaderNotAvailableError, NotLeaderForPartitionError, RequestTimedOutError
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer, TopicPartition
from aiokafka.consumer.subscription_state import ConsumerRebalanceListener


class AIOConsumerRebalanceListener(ConsumerRebalanceListener):
    """处理rebalance事件的监听器"""
    def __init__(self, on_revoke_cb, on_assign_cb):
        self.on_revoke_cb = on_revoke_cb
        self.on_assign_cb = on_assign_cb
    
    async def on_partitions_revoked(self, revoked):
        if self.on_revoke_cb:
            await self.on_revoke_cb(revoked)
    
    async def on_partitions_assigned(self, assigned):
        if self.on_assign_cb:
            await self.on_assign_cb(assigned)


class KafkaConsumerService:
    _consumer = None
    _partitions_assigned_delay = 5  # 多台服务器消费者加入时间不一致时，可能短时间内多次rebalance并 partitions_assigned，所以需要延迟消费时间(秒)
    running = False
    topics = None
    
    @classmethod
    @async_retry(max_attempts=5, delay_seconds=5, retry_on_exceptions=(KafkaConnectionError, KafkaTimeoutError), log_prefix="KafkaConsumerInitialize")
    async def initialize(cls, group_id, topics, batch_size=1, batch_timeout=1.0):
        """初始化消费者"""
        if cls._consumer is not None and cls._consumer._running:
            logger.info(f"Kafka consumer already initialized for topics: {topics} with group_id: {group_id}")
            return

        if cls._consumer is not None and not cls._consumer._running:
            logger.warning(f"Kafka consumer for topics: {topics} with group_id: {group_id} was stopped, restarting...")
            await cls._consumer.stop() # 确保完全停止
            cls._consumer = None
            
        cls.group_id = group_id
        cls.topics = topics
        cls.batch_size = batch_size
        cls.batch_timeout = batch_timeout
        cls.running = True
        
        cls._consumer = AIOKafkaConsumer(
            *topics,
            bootstrap_servers=config.get("KAFKA").get('bootstrap_servers'),
            group_id=group_id,
            auto_offset_reset=config.get("KAFKA").get("offset_reset"),
            enable_auto_commit=False,
            max_poll_records=batch_size,
            session_timeout_ms=30000, # Kafka 服务器认为消费者死亡的时间
            max_poll_interval_ms=600000, # 消费者两次 poll 之间的最大间隔，超过则认为消费者死亡并触发 rebalance
            heartbeat_interval_ms=5000, # 消费者向 Kafka 服务器发送心跳的间隔
            request_timeout_ms=40000 # 客户端请求超时时间，应大于 session_timeout_ms
        )
        listener = AIOConsumerRebalanceListener(
            on_revoke_cb=cls._on_partitions_revoked,
            on_assign_cb=cls._on_partitions_assigned
        )
        # 注册rebalance回调
        cls._consumer.subscribe(
            topics,
            listener=listener
        )
        
        await cls._consumer.start()
        logger.info(f"Kafka consumer initialized for topics: {topics} with group_id: {group_id}")

    @classmethod
    async def _on_partitions_revoked(cls, revoked):
        """分区被撤销时的回调"""
        logger.warning(f"Topics: {cls.topics}, Partitions revoked: {revoked}")

    @classmethod
    async def _on_partitions_assigned(cls, assigned):
        """分区被分配时的回调"""
        logger.warning(f"Topics: {cls.topics}, Partitions assigned: {assigned}")
        
        # 多台服务器消费者加入时间不一致时，可能短时间内多次rebalance并 partitions_assigned，所以需要延迟消费
        logger.info(f"Topics: {cls.topics}, Waiting {cls._partitions_assigned_delay:.2f}s after partitions_assigned...")
        await asyncio.sleep(cls._partitions_assigned_delay)

    @classmethod
    @async_retry(max_attempts=3, delay_seconds=2, retry_on_exceptions=(KafkaTimeoutError, RequestTimedOutError), log_prefix="KafkaConsumeMessages")
    async def consume_messages(cls):
        """消费消息并批量处理"""
        if not cls._consumer or not cls._consumer._running:
            logger.error(f"Topics: {cls.topics}, Consumer not initialized or not running. Attempting to re-initialize...")
            await cls.initialize(cls.group_id, cls.topics, cls.batch_size, cls.batch_timeout) # 尝试重新初始化
            if not cls._consumer or not cls._consumer._running: # 再次检查
                 raise RuntimeError(f"Topics: {cls.topics}, Consumer re-initialization failed.")
        
        try:
            # 批量拉取消息
            msgs = await cls._consumer.getmany(timeout_ms=int(cls.batch_timeout * 1000), max_records=cls.batch_size)
            return msgs
        except (KafkaConnectionError, LeaderNotAvailableError, NotLeaderForPartitionError) as conn_err:
            logger.error(f"Topics: {cls.topics}, Kafka connection error during consume: {conn_err}. Re-initializing consumer.", exc_info=True)
            await cls.stop() # 先停止当前的
            await cls.initialize(cls.group_id, cls.topics, cls.batch_size, cls.batch_timeout) # 重新初始化
            return None # 本次消费失败，等待下次重试
        except KafkaError as ke:
            logger.error(f"Topics: {cls.topics}, Kafka error during consume: {ke}", exc_info=True)
            # 对于其他可恢复的KafkaError，可以根据具体错误类型决定是否需要重连或等待
            await asyncio.sleep(1)
            return None
        except Exception as e:
            logger.error(f"Topics: {cls.topics}, Unexpected error in consumer: {e}", exc_info=True)
            await asyncio.sleep(1)
            return None
    
    @classmethod
    def _get_max_offsets(cls, messages):
        """获取每个分区的最大offset"""
        partitions = {}
        for msg in messages:
            tp = TopicPartition(msg.topic, msg.partition)
            if tp not in partitions or msg.offset > partitions[tp]:
                partitions[tp] = msg.offset
        return partitions
    
    @classmethod
    @async_retry(max_attempts=3, delay_seconds=2, retry_on_exceptions=(CommitFailedError, KafkaTimeoutError, RequestTimedOutError), log_prefix="KafkaCommitOffsets")
    async def _commit_partition_offset(cls, tp, offset):
        """提交单个分区的偏移量"""
        try:
            await cls._consumer.commit({tp: offset + 1})  # 提交下一个偏移量
            # 获取提交后的实际偏移量
            committed_offset = await cls._consumer.committed(tp)
            logger.info(f"Committed offset {committed_offset} for Topic: {tp.topic}, Partition: {tp.partition}")
        except (KafkaConnectionError, LeaderNotAvailableError, NotLeaderForPartitionError) as conn_err:
            logger.error(f"Kafka connection error during commit for {tp} at offset {offset}: {conn_err}. Re-initializing consumer.", exc_info=True)
            await cls.stop()
            await cls.initialize(cls.group_id, cls.topics, cls.batch_size, cls.batch_timeout)
            raise # 抛出异常，让外层重试逻辑捕获并处理
        except CommitFailedError as cfe:
            logger.error(f"Commit failed for {tp} at offset {offset}: {cfe}", exc_info=True)
            # CommitFailedError 通常是由于 rebalance 导致，此时应该重新尝试提交或者放弃本次提交
            # 如果是由于rebalance，分区可能已经分配给其他消费者，此时不应再提交
            # 这里简单地记录错误，具体的恢复逻辑可能需要根据业务场景调整
            raise # 抛出让重试机制处理
        except Exception as e:
            logger.error(f"Error committing offset for {tp}: {e}", exc_info=True)
            raise # 抛出让重试机制处理

    @classmethod
    async def commit_offsets(cls, messages):
        """提交偏移量"""
        if not messages or not cls._consumer or not cls._consumer._running:
            if not cls._consumer or not cls._consumer._running:
                logger.warning(f"Topics: {cls.topics}, Consumer not available for committing offsets. Skipping commit.")
            return
            
        # 取出每个分区的最大偏移量
        partitions = cls._get_max_offsets(messages)
        
        # 为每个分区提交最大偏移量
        commit_tasks = []
        for tp, offset in partitions.items():
            commit_tasks.append(cls._commit_partition_offset(tp, offset))
        
        results = await asyncio.gather(*commit_tasks, return_exceptions=True)
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                tp = list(partitions.keys())[i]
                offset = partitions[tp]
                logger.error(f"Failed to commit offset for {tp} at {offset} after retries: {result}")
                # 此处可以添加更复杂的错误处理，比如将未成功提交的消息重新放回处理队列等
    
    @classmethod
    async def stop(cls):
        """停止消费者"""
        try:
            if cls._consumer:
                await cls._consumer.stop()
                cls._consumer = None
        except Exception as e:
            logger.error(f"Topics: {cls.topics}, Error stopping consumer: {e}")
        cls.running = False


class KafkaProducerService:
    _producer = None
    _lock = asyncio.Lock()
    
    @classmethod
    @async_retry(max_attempts=5, delay_seconds=5, retry_on_exceptions=(KafkaConnectionError, KafkaTimeoutError), log_prefix="KafkaProducerInitialize")
    async def initialize(cls):
        """创建Kafka生产者"""
        async with cls._lock:
            if cls._producer is None or not cls._producer._sender._running:
                if cls._producer and not cls._producer._sender._running:
                    logger.warning("Kafka producer was stopped, restarting...")
                    await cls._producer.stop() # 确保完全停止
                    cls._producer = None

                cls._producer = AIOKafkaProducer(
                    bootstrap_servers=config.get("KAFKA").get('bootstrap_servers'),
                    acks='all', # 确保消息被所有ISR副本确认
                    max_batch_size=16384, # 批处理大小
                    linger_ms=50, # 消息在发送前在缓冲区停留的最长时间，有助于批处理
                    request_timeout_ms=30000, # 生产者请求超时时间
                    # 启用幂等性，防止消息重复发送 (需要 broker >= 0.11.0.0)
                    # enable_idempotence=True 
                )
                await cls._producer.start()
                logger.info("Kafka producer initialized.")
            else:
                logger.info("Kafka producer already initialized.")
    
    @classmethod
    @async_retry(max_attempts=3, delay_seconds=2, retry_on_exceptions=(KafkaTimeoutError, RequestTimedOutError), log_prefix="KafkaSendMessage")
    async def send_message(cls, topic, message, key=None):
        """发送消息到指定topic"""
        try:
            if cls._producer is None or not cls._producer._sender._running:
                logger.warning(f"Producer not initialized or not running for topic {topic}. Attempting to initialize...")
                await cls.initialize()
                if cls._producer is None or not cls._producer._sender._running: # 再次检查
                    raise KafkaConnectionError(f"Failed to initialize producer for topic {topic}.")
            
            if isinstance(message, (dict, list)):
                message_bytes = json.dumps(message, ensure_ascii=False).encode('utf-8')
            else:
                message_bytes = str(message).encode('utf-8')
                
            key_bytes = key.encode('utf-8') if key else None
            
            # send_and_wait 会等待消息被确认
            future = await cls._producer.send(topic, value=message_bytes, key=key_bytes)
            record_metadata = await future # 等待发送完成并获取元数据
            logger.debug(f"Message sent to topic {topic}, partition {record_metadata.partition}, offset {record_metadata.offset}")
            return True
        except (KafkaConnectionError, LeaderNotAvailableError, NotLeaderForPartitionError) as conn_err:
            logger.error(f"Kafka connection error when sending to topic {topic}: {conn_err}. Re-initializing producer.", exc_info=True)
            await cls.close() # 关闭当前可能损坏的连接
            # await cls.initialize() # 重新初始化将在下一次调用时由 async_retry 或显式检查触发
            raise # 抛出让重试机制处理
        except KafkaError as ke:
            logger.error(f"Kafka error when sending to topic {topic}: {ke}", exc_info=True)
            # 对于其他可恢复的KafkaError，可以根据具体错误类型决定是否需要重连或等待
            raise # 抛出让重试机制处理
        except Exception as e:
            logger.error(f"Failed to send message to topic {topic}: {e}", exc_info=True)
            # 对于未知异常，也尝试关闭并让重试机制处理
            await cls.close()
            raise # 抛出让重试机制处理
    
    @classmethod
    async def close(cls):
        """关闭producer连接"""
        async with cls._lock:
            try:
                if cls._producer:
                    await cls._producer.stop()
                    logger.info(f"Producer closed")
            except Exception as e:
                logger.error(f"Failed to close producer: {e}", exc_info=True)
            finally:
                cls._producer = None # 确保在任何情况下都将 _producer 设置为 None


class KafkaService:
    @classmethod
    def _signal_handler(cls, signum, frame):
        """处理系统信号"""
        logger.info(f"Received signal {signum}, shutting down...")
        cls.shutdown()

    @classmethod
    def shutdown(cls):
        """优雅关闭所有资源"""
        logger.info("Shutting down Kafka services...")
        KafkaConsumerService.running = False
    
    @classmethod
    async def _process_message(cls, msg, message_processor, produce_topic=None):
        """处理单条消息"""
        try:
            data = json.loads(msg.value.decode('utf-8'))
            # 记录消息信息
            logger.info(
                f"Processing message - Topic: {msg.topic}, "
                f"Partition: {msg.partition}, "
                f"Offset: {msg.offset}, "
                f"answerId: {data.get('answerId')}"
            )
            # 处理消息
            result, cost_time = await message_processor(data)

            logger.info(f"Process message success - Topic: {msg.topic}, "
                        f"Partition: {msg.partition}, "
                        f"Offset: {msg.offset}, "
                        f"answerId: {data.get('answerId')}, "
                        f"cost time: {cost_time}")
            return result
        except Exception as e:
            logger.error(
                f"Error processing message - Topic: {msg.topic}, "
                f"Partition: {msg.partition}, "
                f"Offset: {msg.offset}, "
                f"message: {msg.value.decode('utf-8')}, "
                f"Error: {str(e)}",
                exc_info=True
            )
            return None

    @classmethod
    async def process_message_batch(cls, messages, message_processor, produce_topic=None):
        """批量处理消息"""
        if not messages:
            return []
            
        # 使用asyncio.gather并发处理消息
        tasks = [
            cls._process_message(msg, message_processor, produce_topic)
            for msg in messages
        ]
        return await asyncio.gather(*tasks, return_exceptions=True)
    
    @classmethod
    async def produce_results(cls, results, all_messages, produce_topic):
        """批量发送处理结果到目标topic"""
        try:
            for idx, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Task failed with error: {result}, Message: {all_messages[idx]}")
                    continue
                elif result is None:
                    logger.warning(f"No result from processor, Message: {all_messages[idx]}")
                    continue
                
                send_result = await KafkaProducerService.send_message(
                    topic=produce_topic,
                    message=result,
                )
                if send_result:
                    logger.info(f"Produced message to {produce_topic}, result: {result}")
                else:
                    logger.warning(f"Failed to produce message to {produce_topic}, result: {result}")
        except Exception as e:
            logger.error(f"Failed to produce results: {e}", exc_info=True)
    
    @classmethod
    async def start_consumer_loop(cls, group_id, topics, message_processor, produce_topic=None, batch_size=1):
        """
        启动消费者循环
        
        Args:
            group_id: 消费者组ID
            topics: 消费的主题
            message_processor: 消息处理函数
            produce_topic: 处理后发送的目标主题(可选)
            batch_size: 批处理大小
        """
        try:
            signal.signal(signal.SIGINT, cls._signal_handler)
            signal.signal(signal.SIGTERM, cls._signal_handler)
            
            # 初始化消费者
            await KafkaConsumerService.initialize(
                group_id=group_id,
                topics=topics,
                batch_size=batch_size
            )
            
            # 开始消费消息
            while KafkaConsumerService.running:
                # 批量拉取消息
                msgs = await KafkaConsumerService.consume_messages()
                if not msgs:
                    continue
                
                all_messages = []
                log_message = f"Consuming messages -"
                # 处理每个分区的消息
                for tp, messages in msgs.items():
                    if not messages:
                        continue
                    
                    """记录分区的消息消费信息"""
                    first_msg = messages[0]
                    last_msg = messages[-1]
                    log_message += f"\tTopic: {tp.topic}, Partition: {tp.partition}, Offsets: {first_msg.offset} to {last_msg.offset}, Count: {len(messages)};"                
                    all_messages.extend(messages)
                logger.info(log_message)
                    
                # 异步批量处理消息
                results = await cls.process_message_batch(
                    messages=all_messages,
                    message_processor=message_processor,
                    produce_topic=produce_topic
                )

                # 如果需要发送到其他topic
                if produce_topic:
                    await cls.produce_results(results, all_messages, produce_topic)
                    
                # 提交偏移量
                await KafkaConsumerService.commit_offsets(all_messages)
                    
        except Exception as e:
            logger.error(f"Failed in consumer loop: {e}", exc_info=True)
        finally:
            await KafkaConsumerService.stop()

    @classmethod
    def start_consuming(cls):
        """启动消费进程"""
        group_id = config["KAFKA"]["group_id"]
        topic = [config["KAFKA"]["insp_data_topic"]]
        produce_topic = config["KAFKA"]["insp_data_result_topic"]
        batch_size = config["KAFKA"]["batch_size"]
        try:
            asyncio.run(cls.start_consumer_loop(group_id, topic, run, produce_topic=produce_topic, batch_size=batch_size))
        except Exception as e:
            logger.error(f"Failed in start consuming: {e}", exc_info=True)
    
    @classmethod
    def start_processes(cls, max_threshold=1):
        """启动多个消费者进程"""
        processes = []
        
        for _ in range(max_threshold):
            p = Process(target=cls.start_consuming)
            p.start()
            processes.append(p)
        
        for p in processes:
            p.join()
