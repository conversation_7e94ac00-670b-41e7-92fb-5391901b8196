from aiokafka.errors import (
    CommitFailedError, KafkaTimeoutError, RequestTimedOutError,
    KafkaUnavailableError, NotLeaderForPartitionError, LeaderNotAvailableError,
    NodeNotReadyError, RebalanceInProgressError, NotCoordinatorForGroupError,
    KafkaConnectionError, NoBrokersAvailable, MetadataEmptyBrokerList,
    TopicAuthorizationFailedError, InvalidTopicError, OffsetOutOfRangeError,
    RecordTooLargeError, UnsupportedVersionError, IllegalStateError,
    InvalidGroupIdError, ProducerClosed, ProducerFenced,UnknownTopicOrPartitionError,
    CorruptRecordException, GroupCoordinatorNotAvailableError,
)
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer, TopicPartition
from aiokafka.consumer.subscription_state import ConsumerRebalanceListener

RETRYABLE_ERRORS = (
    KafkaTimeoutError,
    RequestTimedOutError,
    NotLeaderForPartitionError,
    LeaderNotAvailableError,
    UnknownTopicOrPartitionError,
    GroupCoordinatorNotAvailableError,
    NodeNotReadyError,
    RebalanceInProgressError,
)

CONNECTION_REBUILD_ERRORS = (
    KafkaConnectionError,
    NoBrokersAvailable,
    MetadataEmptyBrokerList,
    ProducerClosed,
)

NON_RETRYABLE_ERRORS = (
    TopicAuthorizationFailedError,
    InvalidTopicError,
    RecordTooLargeError,
    CorruptRecordException,
    UnsupportedVersionError,
    IllegalStateError,
    InvalidGroupIdError,
)